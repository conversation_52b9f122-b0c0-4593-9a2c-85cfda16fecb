# Kinetic Modeling Analysis Web Application
# Веб-приложение для анализа кинетического моделирования

A Streamlit web application for analyzing kinetic data using Pseudo-First Order (PFO) and Pseudo-Second Order (PSO) models.

## Features / Возможности

- **File Upload**: Upload Excel files with kinetic data
- **Automatic Processing**: Data validation and preprocessing
- **Stable Point Detection**: Automatic selection of linear regions (fixed threshold 0.1)
- **Model Fitting**: PFO and PSO kinetic models with curve fitting
- **Performance Metrics**: MAPE and R² calculations
- **Interactive Visualizations**: Plotly and Matplotlib plots
- **Russian Interface**: Complete Russian language interface
- **Results Export**: Download results as Excel files

## Installation / Установка

1. Clone or download this repository
2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Run the application:
```bash
streamlit run app.py
```

## File Structure / هيكل الملفات

```
├── app.py                 # Main Streamlit application
├── data_processor.py      # Data preprocessing functions
├── kinetic_models.py      # PFO and PSO modeling functions
├── visualization.py       # Plotting and visualization functions
├── requirements.txt       # Python dependencies
└── README.md             # This file
```

## Required Data Format / Требуемый формат данных

Your Excel file must contain the following columns:

| Column | Description |
|--------|-------------|
| `т, мин` | Time in minutes |
| `А` | Concentration A |
| `А0` | Initial concentration |
| `А/А0` | Ratio A/A0 |

### Example Data / Пример данных

| т, мин | А   | А0  | А/А0 |
|--------|-----|-----|------|
| 0      | 100 | 100 | 1.0  |
| 5      | 85  | 100 | 0.85 |
| 10     | 72  | 100 | 0.72 |
| 15     | 61  | 100 | 0.61 |
| 20     | 52  | 100 | 0.52 |

## Models / Модели

### Pseudo-First Order (PFO)
- **Equation**: ln(A/A₀) = -k₁ × t
- **Linear form**: ln(A/A₀) vs time
- **Parameter**: k₁ (rate constant, min⁻¹)

### Pseudo-Second Order (PSO)
- **Equation**: 1/A = 1/A₀ + k₂ × t
- **Linear form**: 1/A vs time
- **Parameter**: k₂ (rate constant, L/(mg·min))

## Features Details / Подробности возможностей

### Automatic Stable Point Detection
The application automatically identifies the linear region of your data using slope analysis with a fixed threshold of 0.1 for consistent results.

### Model Performance Metrics
- **MAPE (Mean Absolute Percentage Error)**: Measures prediction accuracy
- **R² (Coefficient of Determination)**: Measures goodness of fit

### Interactive Visualizations
- **Main Plots**: Show experimental data and model fits
- **Residuals Plots**: Help evaluate model performance
- **Downloadable Results**: Export all results to Excel

## Usage Tips / Советы по использованию

1. **Data Quality**: Ensure your data doesn't contain missing values or zeros in concentration columns
2. **Automatic Selection**: The application uses a fixed threshold (0.1) for consistent stable point selection
3. **Model Comparison**: Compare R² and MAPE values to choose the best model
4. **Visual Inspection**: Always check the plots to validate model fits

## Troubleshooting / Устранение неполадок

### Common Issues:
1. **"Missing columns" error**: Check that your Excel file has the exact column names
2. **"No valid data" error**: Ensure your data doesn't contain negative values or zeros
3. **Poor model fit**: Check your data quality and ensure it follows kinetic behavior

### Data Requirements:
- Time values must be positive and increasing
- Concentration values must be positive
- A/A₀ ratios must be between 0 and 1 (typically)

## Extension Ideas / أفكار للتطوير

The modular structure makes it easy to extend the application:

- Add more kinetic models (Zero-order, Elovich, etc.)
- Implement batch processing for multiple files
- Add statistical analysis and confidence intervals
- Include temperature-dependent modeling
- Add data export in different formats

## Dependencies / التبعيات

- streamlit >= 1.28.0
- pandas >= 2.0.0
- numpy >= 1.24.0
- matplotlib >= 3.7.0
- scipy >= 1.10.0
- scikit-learn >= 1.3.0
- openpyxl >= 3.1.0
- plotly >= 5.15.0

## License / الترخيص

This project is open source and available under the MIT License.
