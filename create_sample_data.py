"""
<PERSON><PERSON><PERSON> to create sample Excel data for testing the kinetic modeling application.
"""

import pandas as pd
import numpy as np

# Create sample kinetic data
np.random.seed(42)  # For reproducible results

# Time points (minutes)
time = np.array([0, 2, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60])

# Simulate PFO kinetics with some noise
k1_true = 0.05  # True rate constant
A0 = 100  # Initial concentration

# Generate true PFO data
A_A0_true = np.exp(-k1_true * time)
A_true = A0 * A_A0_true

# Add some realistic noise
noise_level = 0.02
A_measured = A_true * (1 + np.random.normal(0, noise_level, len(time)))
A_A0_measured = A_measured / A0

# Ensure no negative values
A_measured = np.maximum(A_measured, 0.1)
A_A0_measured = np.maximum(A_A0_measured, 0.001)

# Create DataFrame
sample_data = pd.DataFrame({
    'т, мин': time,
    'А': A_measured,
    'А0': np.full(len(time), A0),
    'А/А0': A_A0_measured
})

# Round values for cleaner presentation
sample_data['А'] = sample_data['А'].round(2)
sample_data['А/А0'] = sample_data['А/А0'].round(4)

# Save to Excel
sample_data.to_excel('sample_kinetic_data.xlsx', index=False)

print("Sample data created successfully!")
print("\nSample data preview:")
print(sample_data.head(10))

print(f"\nData characteristics:")
print(f"Time range: {time[0]} - {time[-1]} minutes")
print(f"Initial concentration: {A0}")
print(f"Final A/A0 ratio: {A_A0_measured[-1]:.4f}")
print(f"True k1 value: {k1_true} min⁻¹")
