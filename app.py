"""
Streamlit Web Application for Kinetic Modeling Analysis
Веб-приложение для анализа кинетического моделирования
"""

import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
from io import BytesIO

# Import custom modules
from data_processor import validate_excel_structure, preprocess_data, get_data_summary
from kinetic_models import (
    find_stable_points, fit_pfo_model, fit_pso_model,
    create_results_summary, create_detailed_results
)
from visualization import create_matplotlib_plots, create_plotly_plots, create_residuals_plot

# Configure page
st.set_page_config(
    page_title="Анализ кинетического моделирования",
    page_icon="🧪",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
.metric-container {
    background-color: #f0f2f6;
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 0.5rem 0;
}
</style>
""", unsafe_allow_html=True)

def main():
    st.title("🧪 Анализ кинетического моделирования")
    st.markdown("---")

    # Sidebar for parameters
    with st.sidebar:
        st.header("⚙️ Параметры")

        # Plot type selection
        plot_type = st.selectbox(
            "Тип графика",
            ["Статический (Matplotlib)"],
            index=0
        )

        st.markdown("---")
        st.markdown("### 📋 Требования к файлу")
        st.markdown("""
        **Обязательные столбцы:**
        - `т, мин` (Время в минутах)
        - `А` (Концентрация A)
        - `А0` (Начальная концентрация)
        - `А/А0` (Отношение A/A0)
        """)

    # File upload
    st.header("📁 Загрузка файла")
    uploaded_file = st.file_uploader(
        "Выберите файл Excel",
        type=['xlsx', 'xls'],
        help="Загрузите файл Excel с кинетическими данными"
    )

    if uploaded_file is not None:
        try:
            # Read the Excel file
            df = pd.read_excel(uploaded_file)

            # Validate structure
            is_valid, error_message = validate_excel_structure(df)

            if not is_valid:
                st.error(f"{error_message}")
                return

            st.success("Файл успешно загружен!")

            # Show raw data preview
            with st.expander("👀 Предварительный просмотр данных"):
                st.dataframe(df.head(10))
                st.info(f"Общее количество строк: {len(df)}")

            # Process data
            processed_df = preprocess_data(df)

            if len(processed_df) == 0:
                st.error("Нет действительных данных после обработки")
                return

            # Data summary
            summary = get_data_summary(processed_df)

            st.header("Сводка данных")
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("Действительные точки", summary['total_points'])
            with col2:
                st.metric("Диапазон времени", f"{summary['time_range'][0]:.1f} - {summary['time_range'][1]:.1f} мин")
            with col3:
                st.metric("Начальная концентрация", f"{summary['a0_value']:.3f}")
            with col4:
                st.metric("Диапазон A/A0", f"{summary['a_a0_range'][0]:.3f} - {summary['a_a0_range'][1]:.3f}")

            # Find stable points (using fixed threshold of 0.1)
            stable_indices = find_stable_points(processed_df['ln_A_A0'], processed_df['т, мин'], 0.1)
            selected_data = processed_df.iloc[stable_indices].copy()

            st.header("🎯 Выбранные точки")
            st.info(f"Выбрано {len(selected_data)} точек из {len(processed_df)}")
            st.info(f"Выбранный временной диапазон: {selected_data['т, мин'].iloc[0]:.1f} - {selected_data['т, мин'].iloc[-1]:.1f} мин")

            # Fit models
            try:
                k1, pfo_predictions, mape_pfo, r2_pfo = fit_pfo_model(selected_data)
                k2, pso_predictions, mape_pso, r2_pso = fit_pso_model(selected_data)

                # Results summary
                st.header("📈 Результаты моделирования")

                results_summary = create_results_summary(k1, k2, mape_pfo, mape_pso, r2_pfo, r2_pso)

                # Display summary table
                st.subheader("Сводка результатов")
                st.dataframe(results_summary, use_container_width=True)

                # Model comparison metrics
                col1, col2 = st.columns(2)
                with col1:
                    st.markdown("### Модель PFO")
                    st.metric("R² Score", f"{r2_pfo:.4f}")
                    st.metric("MAPE (%)", f"{mape_pfo:.2f}")
                    st.metric("Константа скорости k₁", f"{abs(k1):.5f} мин⁻¹")

                with col2:
                    st.markdown("### Модель PSO")
                    st.metric("R² Score", f"{r2_pso:.4f}")
                    st.metric("MAPE (%)", f"{mape_pso:.2f}")
                    st.metric("Константа скорости k₂", f"{k2:.5f} л/(мг·мин)")

                # Detailed results
                with st.expander("🔍 Подробные результаты"):
                    detailed_results = create_detailed_results(pfo_predictions, pso_predictions)
                    st.dataframe(detailed_results, use_container_width=True)

                # Plots
                st.header("Графики")

                if plot_type == "Интерактивный (Plotly)":
                    # Main plots
                    fig_main = create_plotly_plots(processed_df, selected_data, pfo_predictions, pso_predictions, k1, k2)
                    st.plotly_chart(fig_main, use_container_width=True)

                    # Residuals plot
                    fig_residuals = create_residuals_plot(pfo_predictions, pso_predictions)
                    st.plotly_chart(fig_residuals, use_container_width=True)

                else:  # Matplotlib
                    fig_main = create_matplotlib_plots(processed_df, selected_data, pfo_predictions, pso_predictions, k1, k2)
                    st.pyplot(fig_main)

                # Download results
                st.header("Скачать результаты")

                # Prepare download data
                download_data = {
                    'Summary': results_summary,
                    'Detailed_Results': detailed_results,
                    'Selected_Data': selected_data,
                    'PFO_Predictions': pfo_predictions,
                    'PSO_Predictions': pso_predictions
                }

                # Create Excel file for download
                output = BytesIO()
                with pd.ExcelWriter(output, engine='openpyxl') as writer:
                    for sheet_name, data in download_data.items():
                        data.to_excel(writer, sheet_name=sheet_name, index=False)

                st.download_button(
                    label="📥 Скачать результаты как файл Excel",
                    data=output.getvalue(),
                    file_name="kinetic_modeling_results.xlsx",
                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                )

            except Exception as e:
                st.error(f"Ошибка моделирования: {str(e)}")

        except Exception as e:
            st.error(f"Ошибка чтения файла: {str(e)}")

    else:
        # Instructions when no file is uploaded
        st.info("Пожалуйста, загрузите файл Excel для начала анализа")


if __name__ == "__main__":
    main()
