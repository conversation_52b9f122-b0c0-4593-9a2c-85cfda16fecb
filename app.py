"""
Streamlit Web Application for Kinetic Modeling Analysis
تطبيق ويب لتحليل النمذجة الحركية
"""

import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
from io import BytesIO

# Import custom modules
from data_processor import validate_excel_structure, preprocess_data, get_data_summary
from kinetic_models import (
    find_stable_points, fit_pfo_model, fit_pso_model, 
    create_results_summary, create_detailed_results
)
from visualization import create_matplotlib_plots, create_plotly_plots, create_residuals_plot

# Configure page
st.set_page_config(
    page_title="تحليل النمذجة الحركية / Kinetic Modeling Analysis",
    page_icon="🧪",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better Arabic support
st.markdown("""
<style>
.rtl {
    direction: rtl;
    text-align: right;
}
.metric-container {
    background-color: #f0f2f6;
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 0.5rem 0;
}
</style>
""", unsafe_allow_html=True)

def main():
    st.title("🧪 تحليل النمذجة الحركية / Kinetic Modeling Analysis")
    st.markdown("---")
    
    # Sidebar for parameters
    with st.sidebar:
        st.header("⚙️ المعاملات / Parameters")
        
        # Threshold for stable point detection
        threshold = st.slider(
            "عتبة النقاط المستقرة / Stable Points Threshold",
            min_value=0.01,
            max_value=0.5,
            value=0.1,
            step=0.01,
            help="Lower values = more strict selection of stable points"
        )
        
        # Plot type selection
        plot_type = st.selectbox(
            "نوع الرسم البياني / Plot Type",
            ["Interactive (Plotly)", "Static (Matplotlib)"],
            index=0
        )
        
        st.markdown("---")
        st.markdown("### 📋 متطلبات الملف / File Requirements")
        st.markdown("""
        **Required columns / الأعمدة المطلوبة:**
        - `т, мин` (Time in minutes)
        - `А` (Concentration A)
        - `А0` (Initial concentration)
        - `А/А0` (Ratio A/A0)
        """)
    
    # File upload
    st.header("📁 رفع الملف / File Upload")
    uploaded_file = st.file_uploader(
        "اختر ملف Excel / Choose Excel file",
        type=['xlsx', 'xls'],
        help="Upload an Excel file with kinetic data"
    )
    
    if uploaded_file is not None:
        try:
            # Read the Excel file
            df = pd.read_excel(uploaded_file)
            
            # Validate structure
            is_valid, error_message = validate_excel_structure(df)
            
            if not is_valid:
                st.error(f"❌ {error_message}")
                return
            
            st.success("✅ تم رفع الملف بنجاح / File uploaded successfully!")
            
            # Show raw data preview
            with st.expander("👀 معاينة البيانات الخام / Raw Data Preview"):
                st.dataframe(df.head(10))
                st.info(f"إجمالي الصفوف / Total rows: {len(df)}")
            
            # Process data
            processed_df = preprocess_data(df)
            
            if len(processed_df) == 0:
                st.error("❌ لا توجد بيانات صالحة بعد المعالجة / No valid data after processing")
                return
            
            # Data summary
            summary = get_data_summary(processed_df)
            
            st.header("📊 ملخص البيانات / Data Summary")
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("النقاط الصالحة / Valid Points", summary['total_points'])
            with col2:
                st.metric("نطاق الوقت / Time Range", f"{summary['time_range'][0]:.1f} - {summary['time_range'][1]:.1f} min")
            with col3:
                st.metric("التركيز الأولي / Initial Conc.", f"{summary['a0_value']:.3f}")
            with col4:
                st.metric("نطاق A/A0", f"{summary['a_a0_range'][0]:.3f} - {summary['a_a0_range'][1]:.3f}")
            
            # Find stable points
            stable_indices = find_stable_points(processed_df['ln_A_A0'], processed_df['т, мин'], threshold)
            selected_data = processed_df.iloc[stable_indices].copy()
            
            st.header("🎯 النقاط المختارة / Selected Points")
            st.info(f"تم اختيار {len(selected_data)} نقطة من أصل {len(processed_df)} / Selected {len(selected_data)} points out of {len(processed_df)}")
            st.info(f"النطاق الزمني المختار / Selected time range: {selected_data['т, мин'].iloc[0]:.1f} - {selected_data['т, мин'].iloc[-1]:.1f} min")
            
            # Fit models
            try:
                k1, pfo_predictions, mape_pfo, r2_pfo = fit_pfo_model(selected_data)
                k2, pso_predictions, mape_pso, r2_pso = fit_pso_model(selected_data)
                
                # Results summary
                st.header("📈 نتائج النمذجة / Modeling Results")
                
                results_summary = create_results_summary(k1, k2, mape_pfo, mape_pso, r2_pfo, r2_pso)
                
                # Display summary table
                st.subheader("ملخص النتائج / Results Summary")
                st.dataframe(results_summary, use_container_width=True)
                
                # Model comparison metrics
                col1, col2 = st.columns(2)
                with col1:
                    st.markdown("### PFO Model")
                    st.metric("R² Score", f"{r2_pfo:.4f}")
                    st.metric("MAPE (%)", f"{mape_pfo:.2f}")
                    st.metric("Rate Constant k₁", f"{abs(k1):.5f} min⁻¹")
                
                with col2:
                    st.markdown("### PSO Model")
                    st.metric("R² Score", f"{r2_pso:.4f}")
                    st.metric("MAPE (%)", f"{mape_pso:.2f}")
                    st.metric("Rate Constant k₂", f"{k2:.5f} L/(mg·min)")
                
                # Detailed results
                with st.expander("🔍 النتائج التفصيلية / Detailed Results"):
                    detailed_results = create_detailed_results(pfo_predictions, pso_predictions)
                    st.dataframe(detailed_results, use_container_width=True)
                
                # Plots
                st.header("📊 الرسوم البيانية / Plots")
                
                if plot_type == "Interactive (Plotly)":
                    # Main plots
                    fig_main = create_plotly_plots(processed_df, selected_data, pfo_predictions, pso_predictions, k1, k2)
                    st.plotly_chart(fig_main, use_container_width=True)
                    
                    # Residuals plot
                    fig_residuals = create_residuals_plot(pfo_predictions, pso_predictions)
                    st.plotly_chart(fig_residuals, use_container_width=True)
                    
                else:  # Matplotlib
                    fig_main = create_matplotlib_plots(processed_df, selected_data, pfo_predictions, pso_predictions, k1, k2)
                    st.pyplot(fig_main)
                
                # Download results
                st.header("💾 تحميل النتائج / Download Results")
                
                # Prepare download data
                download_data = {
                    'Summary': results_summary,
                    'Detailed_Results': detailed_results,
                    'Selected_Data': selected_data,
                    'PFO_Predictions': pfo_predictions,
                    'PSO_Predictions': pso_predictions
                }
                
                # Create Excel file for download
                output = BytesIO()
                with pd.ExcelWriter(output, engine='openpyxl') as writer:
                    for sheet_name, data in download_data.items():
                        data.to_excel(writer, sheet_name=sheet_name, index=False)
                
                st.download_button(
                    label="📥 تحميل النتائج كملف Excel / Download Results as Excel",
                    data=output.getvalue(),
                    file_name="kinetic_modeling_results.xlsx",
                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                )
                
            except Exception as e:
                st.error(f"❌ خطأ في النمذجة / Modeling error: {str(e)}")
                
        except Exception as e:
            st.error(f"❌ خطأ في قراءة الملف / File reading error: {str(e)}")
    
    else:
        # Instructions when no file is uploaded
        st.info("👆 يرجى رفع ملف Excel للبدء في التحليل / Please upload an Excel file to start analysis")
        
        # Show example data structure
        st.subheader("📋 مثال على هيكل البيانات / Example Data Structure")
        example_data = pd.DataFrame({
            'т, мин': [0, 5, 10, 15, 20, 25, 30],
            'А': [100, 85, 72, 61, 52, 44, 37],
            'А0': [100, 100, 100, 100, 100, 100, 100],
            'А/А0': [1.0, 0.85, 0.72, 0.61, 0.52, 0.44, 0.37]
        })
        st.dataframe(example_data)


if __name__ == "__main__":
    main()
