"""
Data processing module for kinetic modeling analysis.
"""

import pandas as pd
import numpy as np
from typing import Tuple, Optional


def validate_excel_structure(df: pd.DataFrame) -> Tuple[bool, str]:
    """
    Validate that the uploaded Excel file has the required structure.
    
    Args:
        df: DataFrame to validate
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    required_columns = ['т, мин', 'А', 'А0', 'А/А0']
    
    if df.empty:
        return False, "الملف فارغ / File is empty"
    
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        return False, f"الأعمدة المفقودة / Missing columns: {', '.join(missing_columns)}"
    
    return True, ""


def preprocess_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Preprocess the kinetic data by cleaning and calculating derived columns.
    
    Args:
        df: Raw DataFrame from Excel file
        
    Returns:
        Processed DataFrame with additional columns
    """
    # Create a copy to avoid modifying the original
    processed_df = df.copy()
    
    # Convert columns to numeric, handling errors
    for col in ['т, мин', 'А', 'А0', 'А/А0']:
        processed_df[col] = pd.to_numeric(processed_df[col], errors='coerce')
    
    # Drop rows with NaN values
    processed_df.dropna(subset=['т, мин', 'А', 'А0', 'А/А0'], inplace=True)
    
    # Filter out non-positive values in 'А/А0' for logarithm calculation
    if (processed_df['А/А0'] <= 0).any():
        processed_df = processed_df[processed_df['А/А0'] > 0].copy()
    
    # Filter out zero or negative values in 'А' for inverse calculation
    if (processed_df['А'] <= 0).any():
        processed_df = processed_df[processed_df['А'] > 0].copy()
    
    # Calculate derived columns
    processed_df['ln_A_A0'] = np.log(processed_df['А/А0'])
    processed_df['inv_A'] = 1 / processed_df['А']
    
    return processed_df


def get_data_summary(df: pd.DataFrame) -> dict:
    """
    Get summary statistics of the processed data.
    
    Args:
        df: Processed DataFrame
        
    Returns:
        Dictionary with summary statistics
    """
    return {
        'total_points': len(df),
        'time_range': (df['т, мин'].min(), df['т, мин'].max()),
        'a_range': (df['А'].min(), df['А'].max()),
        'a0_value': df['А0'].iloc[0] if len(df) > 0 else None,
        'a_a0_range': (df['А/А0'].min(), df['А/А0'].max())
    }
