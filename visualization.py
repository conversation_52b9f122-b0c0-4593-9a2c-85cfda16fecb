"""
Visualization functions for kinetic modeling analysis.
"""

import matplotlib.pyplot as plt
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from typing import Tuple


def create_matplotlib_plots(df: pd.DataFrame, selected_data: pd.DataFrame, 
                           pfo_predictions: pd.DataFrame, pso_predictions: pd.DataFrame,
                           k1: float, k2: float) -> plt.Figure:
    """
    Create matplotlib plots for PFO and PSO models.
    
    Args:
        df: Full dataset
        selected_data: Selected stable points
        pfo_predictions: PFO model predictions
        pso_predictions: PSO model predictions
        k1: PFO rate constant
        k2: PSO rate constant
        
    Returns:
        Matplotlib figure
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # PFO plot
    ax1.plot(df['т, мин'], df['ln_A_A0'], 'bo-', label='البيانات التجريبية / Experimental Data (ln(A/A₀))', markersize=4)
    ax1.plot(pfo_predictions['т, мин'], pfo_predictions['PFO_pred_ln'], 'r--', 
             label=f'نموذج PFO / PFO Model (k₁={abs(k1):.5f})', linewidth=2)
    ax1.set_title('نموذج الرتبة الأولى الزائفة / Pseudo-First Order Model')
    ax1.set_xlabel('الوقت، دقيقة / Time, min')
    ax1.set_ylabel('ln(A/A₀)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # PSO plot
    ax2.plot(df['т, мин'], df['inv_A'], 'go-', label='البيانات التجريبية / Experimental Data (1/A)', markersize=4)
    ax2.plot(pso_predictions['т, мин'], pso_predictions['PSO_pred_inv'], 'r--', 
             label=f'نموذج PSO / PSO Model (k₂={k2:.5f})', linewidth=2)
    ax2.set_title('نموذج الرتبة الثانية الزائفة / Pseudo-Second Order Model')
    ax2.set_xlabel('الوقت، دقيقة / Time, min')
    ax2.set_ylabel('1/A')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    return fig


def create_plotly_plots(df: pd.DataFrame, selected_data: pd.DataFrame, 
                       pfo_predictions: pd.DataFrame, pso_predictions: pd.DataFrame,
                       k1: float, k2: float) -> go.Figure:
    """
    Create interactive Plotly plots for PFO and PSO models.
    
    Args:
        df: Full dataset
        selected_data: Selected stable points
        pfo_predictions: PFO model predictions
        pso_predictions: PSO model predictions
        k1: PFO rate constant
        k2: PSO rate constant
        
    Returns:
        Plotly figure
    """
    fig = make_subplots(
        rows=1, cols=2,
        subplot_titles=('نموذج الرتبة الأولى الزائفة / Pseudo-First Order Model', 
                       'نموذج الرتبة الثانية الزائفة / Pseudo-Second Order Model'),
        x_titles=['الوقت، دقيقة / Time, min', 'الوقت، دقيقة / Time, min'],
        y_titles=['ln(A/A₀)', '1/A']
    )
    
    # PFO plot
    fig.add_trace(
        go.Scatter(
            x=df['т, мин'], 
            y=df['ln_A_A0'],
            mode='markers+lines',
            name='البيانات التجريبية / Experimental Data (ln(A/A₀))',
            marker=dict(color='blue', size=6),
            line=dict(color='blue', width=1)
        ),
        row=1, col=1
    )
    
    fig.add_trace(
        go.Scatter(
            x=pfo_predictions['т, мин'], 
            y=pfo_predictions['PFO_pred_ln'],
            mode='lines',
            name=f'نموذج PFO / PFO Model (k₁={abs(k1):.5f})',
            line=dict(color='red', width=3, dash='dash')
        ),
        row=1, col=1
    )
    
    # PSO plot
    fig.add_trace(
        go.Scatter(
            x=df['т, мин'], 
            y=df['inv_A'],
            mode='markers+lines',
            name='البيانات التجريبية / Experimental Data (1/A)',
            marker=dict(color='green', size=6),
            line=dict(color='green', width=1)
        ),
        row=1, col=2
    )
    
    fig.add_trace(
        go.Scatter(
            x=pso_predictions['т, мин'], 
            y=pso_predictions['PSO_pred_inv'],
            mode='lines',
            name=f'نموذج PSO / PSO Model (k₂={k2:.5f})',
            line=dict(color='red', width=3, dash='dash')
        ),
        row=1, col=2
    )
    
    fig.update_layout(
        height=500,
        showlegend=True,
        title_text="تحليل النماذج الحركية / Kinetic Models Analysis"
    )
    
    return fig


def create_residuals_plot(pfo_predictions: pd.DataFrame, pso_predictions: pd.DataFrame) -> go.Figure:
    """
    Create residuals plot for model evaluation.
    
    Args:
        pfo_predictions: PFO model predictions
        pso_predictions: PSO model predictions
        
    Returns:
        Plotly figure with residuals
    """
    fig = make_subplots(
        rows=1, cols=2,
        subplot_titles=('PFO Residuals', 'PSO Residuals'),
        x_titles=['Predicted', 'Predicted'],
        y_titles=['Residuals', 'Residuals']
    )
    
    # PFO residuals
    pfo_residuals = pfo_predictions['А/А0'] - pfo_predictions['PFO_pred']
    fig.add_trace(
        go.Scatter(
            x=pfo_predictions['PFO_pred'],
            y=pfo_residuals,
            mode='markers',
            name='PFO Residuals',
            marker=dict(color='blue', size=8)
        ),
        row=1, col=1
    )
    
    # Add zero line for PFO
    fig.add_hline(y=0, line_dash="dash", line_color="red", row=1, col=1)
    
    # PSO residuals
    pso_residuals = pso_predictions['А'] - pso_predictions['PSO_pred']
    fig.add_trace(
        go.Scatter(
            x=pso_predictions['PSO_pred'],
            y=pso_residuals,
            mode='markers',
            name='PSO Residuals',
            marker=dict(color='green', size=8)
        ),
        row=1, col=2
    )
    
    # Add zero line for PSO
    fig.add_hline(y=0, line_dash="dash", line_color="red", row=1, col=2)
    
    fig.update_layout(
        height=400,
        title_text="تحليل البواقي / Residuals Analysis"
    )
    
    return fig
